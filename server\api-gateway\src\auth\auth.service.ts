/**
 * 认证服务
 */
import { Injectable, Logger, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 验证用户
   */
  async validateUser(usernameOrEmail: string, password: string): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'validateUser' }, { usernameOrEmail, password }),
      );
      return user;
    } catch (error: any) {
      this.logger.error('用户验证失败', error);

      // 处理微服务返回的错误
      if (error?.message) {
        if (error.message.includes('用户不存在')) {
          throw new UnauthorizedException('用户不存在');
        }
        if (error.message.includes('密码错误')) {
          throw new UnauthorizedException('密码错误');
        }
      }

      throw new UnauthorizedException('用户名/邮箱或密码错误');
    }
  }

  /**
   * 登录
   */
  async login(user: any) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  /**
   * 注册
   */
  async register(username: string, email: string, password: string, displayName?: string) {
    try {
      const result = await firstValueFrom(
        this.userService.send({ cmd: 'register' }, { username, email, password, displayName }),
      );
      return result;
    } catch (error: any) {
      this.logger.error('用户注册失败', error);
      this.logger.error('错误详情:', JSON.stringify(error, null, 2));

      // 处理RpcException格式的错误
      if (error?.error) {
        const rpcError = error.error;
        this.logger.error('RPC错误消息:', rpcError.message);

        if (rpcError.message && rpcError.message.includes('用户名已存在')) {
          throw new ConflictException('用户名已存在');
        }
        if (rpcError.message && rpcError.message.includes('邮箱已存在')) {
          throw new ConflictException('邮箱已存在');
        }
        if (rpcError.message && rpcError.message.includes('用户不存在')) {
          throw new NotFoundException('用户不存在');
        }

        // 抛出带有原始消息的错误
        throw new Error(rpcError.message || '注册失败');
      }

      // 处理直接的错误消息
      if (error?.message) {
        this.logger.error('直接错误消息:', error.message);
        if (error.message.includes('用户名已存在')) {
          throw new ConflictException('用户名已存在');
        }
        if (error.message.includes('邮箱已存在')) {
          throw new ConflictException('邮箱已存在');
        }
        if (error.message.includes('用户不存在')) {
          throw new NotFoundException('用户不存在');
        }
      }

      // 默认抛出原始错误
      throw error;
    }
  }

  /**
   * 验证JWT
   */
  async validateJwt(payload: any): Promise<any> {
    try {
      const user = await firstValueFrom(
        this.userService.send({ cmd: 'findUserById' }, payload.sub),
      );
      return user;
    } catch (error) {
      this.logger.error('JWT验证失败', error);
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
}
