/**
 * JWT认证守卫
 */
import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('缺少认证令牌');
    }

    const [type, token] = authHeader.split(' ');

    if (type !== 'Bearer') {
      throw new UnauthorizedException('无效的认证类型');
    }

    try {
      // 验证JWT
      const payload = this.jwtService.verify(token);

      // 创建简化的用户对象，避免微服务调用
      const user = {
        id: payload.sub || payload.userId || 'default-user',
        username: payload.username || 'user',
        email: payload.email || '<EMAIL>',
        role: payload.role || 'user'
      };

      // 将用户信息添加到请求中
      request.user = user;

      return true;
    } catch (error) {
      // 移除开发环境的自动登录逻辑，确保所有环境都需要有效的JWT token
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
}
