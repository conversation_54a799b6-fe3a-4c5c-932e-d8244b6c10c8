-- 创建数据库
CREATE DATABASE IF NOT EXISTS dl_engine_registry;
CREATE DATABASE IF NOT EXISTS dl_engine_users;
CREATE DATABASE IF NOT EXISTS dl_engine_projects;
CREATE DATABASE IF NOT EXISTS dl_engine_assets;
CREATE DATABASE IF NOT EXISTS dl_engine_render;

-- 监控服务数据库
CREATE DATABASE IF NOT EXISTS monitoring;

-- 游戏服务器数据库
CREATE DATABASE IF NOT EXISTS dl_engine_games;


-- 使用用户数据库
USE dl_engine_users;

-- 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  displayName VARCHAR(255),
  isVerified BOOLEAN DEFAULT FALSE,
  isGuest BOOLEAN DEFAULT FALSE,
  role ENUM('user', 'admin') DEFAULT 'user',
  inviteCode VARCHAR(255),
  lastLoginAt DATETIME,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建管理员用户（如果不存在）
INSERT IGNORE INTO users (id, username, email, password, displayName, isVerified, role, createdAt, updatedAt)
VALUES (
  UUID(),
  'admin',
  '<EMAIL>',
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf2Op6YO8yvbfEE9eaYasXi', -- 密码: admin123
  '管理员',
  1,
  'admin',
  NOW(),
  NOW()
);

-- 创建测试用户（如果不存在）
INSERT IGNORE INTO users (id, username, email, password, displayName, isVerified, role, createdAt, updatedAt)
VALUES (
  UUID(),
  'testuser',
  '<EMAIL>',
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MbGcD3wlf2Op6YO8yvbfEE9eaYasXi', -- 密码: admin123
  '测试用户',
  1,
  'user',
  NOW(),
  NOW()
);
