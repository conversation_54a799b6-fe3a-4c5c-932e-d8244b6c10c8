/**
 * 认证控制器
 */
import { Controller, Post, Body, UseGuards, Get, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern, RpcException } from '@nestjs/microservices';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  async login(@Request() req, @Body() loginDto: LoginDto) {
    return this.authService.login(req.user);
  }

  @Post('register')
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({ status: 201, description: '注册成功' })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(
      registerDto.username,
      registerDto.email,
      registerDto.password,
      registerDto.displayName,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({ status: 200, description: '返回当前用户信息' })
  getProfile(@Request() req) {
    return req.user;
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'validateUser' })
  async validateUser(data: { usernameOrEmail: string; password: string }) {
    try {
      return this.authService.validateUser(data.usernameOrEmail, data.password);
    } catch (error) {
      throw new RpcException({
        status: 'error',
        message: error.message || '用户验证失败',
        code: error.status || 500
      });
    }
  }

  @MessagePattern({ cmd: 'validateJwt' })
  async validateJwt(payload: any) {
    try {
      return this.authService.validateJwt(payload);
    } catch (error) {
      throw new RpcException({
        status: 'error',
        message: error.message || 'JWT验证失败',
        code: error.status || 500
      });
    }
  }

  @MessagePattern({ cmd: 'register' })
  async handleRegister(data: { username: string; email: string; password: string; displayName?: string }) {
    try {
      return this.authService.register(data.username, data.email, data.password, data.displayName);
    } catch (error) {
      // 将异常转换为可序列化的格式
      throw new RpcException({
        status: 'error',
        message: error.message || '注册失败',
        code: error.status || 500
      });
    }
  }

  @MessagePattern({ cmd: 'login' })
  async handleLogin(data: { usernameOrEmail: string; password: string }) {
    const user = await this.authService.validateUser(data.usernameOrEmail, data.password);
    if (!user) {
      throw new Error('Invalid credentials');
    }
    return this.authService.login(user);
  }

  @MessagePattern({ cmd: 'findUserById' })
  async findUserById(userId: string) {
    return this.authService.findUserById(userId);
  }
}
