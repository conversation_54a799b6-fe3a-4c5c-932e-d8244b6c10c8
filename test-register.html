<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>用户注册测试</h1>
    
    <form id="registerForm">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit">注册</button>
    </form>
    
    <div id="result"></div>
    
    <h2>快速测试</h2>
    <button onclick="testNewUser()">测试新用户注册</button>
    <button onclick="testDuplicateUser()">测试重复用户名</button>
    
    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            await registerUser(username, email, password);
        });
        
        async function registerUser(username, email, password) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '注册中...';
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors',
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `注册成功！<br>用户ID: ${data.data.user.id}<br>用户名: ${data.data.user.username}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `注册失败: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `网络错误: ${error.message}<br>详细信息: ${JSON.stringify(error)}`;
                console.error('注册错误:', error);
            }
        }
        
        async function testNewUser() {
            const timestamp = Date.now();
            await registerUser(`testuser${timestamp}`, `test${timestamp}@example.com`, 'password123');
        }
        
        async function testDuplicateUser() {
            // 先注册一个用户
            const username = 'duplicate_test';
            await registerUser(username, '<EMAIL>', 'password123');
            
            // 等待一秒后尝试注册相同用户名
            setTimeout(async () => {
                await registerUser(username, '<EMAIL>', 'password123');
            }, 1000);
        }
    </script>
</body>
</html>
