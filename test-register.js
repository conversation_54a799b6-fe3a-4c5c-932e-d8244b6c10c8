// 测试注册功能的简单脚本
const axios = require('axios');

async function testRegister() {
  try {
    console.log('测试用户注册...');
    
    const response = await axios.post('http://localhost:3000/api/auth/register', {
      username: 'testuser' + Date.now(),
      email: 'test' + Date.now() + '@example.com',
      password: 'password123'
    });
    
    console.log('注册成功:', response.data);
  } catch (error) {
    console.error('注册失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data);
    console.error('完整错误:', error.message);
  }
}

// 测试重复用户名
async function testDuplicateUsername() {
  try {
    console.log('\n测试重复用户名...');
    
    const username = 'duplicate_test_user';
    const email1 = 'test1_' + Date.now() + '@example.com';
    const email2 = 'test2_' + Date.now() + '@example.com';
    
    // 第一次注册
    console.log('第一次注册...');
    await axios.post('http://localhost:3000/api/auth/register', {
      username: username,
      email: email1,
      password: 'password123'
    });
    console.log('第一次注册成功');
    
    // 第二次注册（应该失败）
    console.log('第二次注册（相同用户名）...');
    await axios.post('http://localhost:3000/api/auth/register', {
      username: username,
      email: email2,
      password: 'password123'
    });
    console.log('第二次注册也成功了？这不应该发生！');
    
  } catch (error) {
    console.log('第二次注册失败（预期行为）:');
    console.log('状态码:', error.response?.status);
    console.log('错误信息:', error.response?.data);
  }
}

async function runTests() {
  await testRegister();
  await testDuplicateUsername();
}

runTests();
